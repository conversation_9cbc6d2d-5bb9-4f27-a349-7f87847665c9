# 基础设施连接配置
CONSUL_HOST=consul
CONSUL_PORT=8500

# 如果基础设施在其他主机，请修改为实际IP
# CONSUL_HOST=*************

# 服务配置
SERVICE_NAME=keyword-analysis-service
SERVICE_PORT=8002
SERVICE_TAGS=api,business,keyword-analysis
ENVIRONMENT=production
HOST=0.0.0.0

# 数据库连接配置
PG_HOST=************
PG_PORT=5432
PG_USER=lens
PG_PASSWORD=Ls.3956573
PG_DB=lens

# 如果使用外部数据库，请修改为实际连接信息
# PG_HOST=*************
# DATABASE_URL=*********************************************/mydb

# Wildberries API配置
WB_API_KEYS=**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
WB_ADV_API_BASE_URL=https://advert-api.wildberries.ru
WB_ANALYTICS_API_BASE_URL=https://seller-analytics-api.wildberries.ru

# API请求配置
REQUEST_TIMEOUT=30
MAX_RETRIES=3
RATE_LIMIT_REQUESTS_PER_SECOND=1

# 分析规则配置（可选，使用默认值）
# EXCLUDE_IRRELEVANT_SIMILARITY_THRESHOLD=30
# EXCLUDE_IRRELEVANT_COUNT_THRESHOLD=0
# EXCLUDE_ZEROCLICK_MIN_SUM=50
# EXCLUDE_NODATA_MIN_SUM=20
# OBSERVE_LOWCTR_MIN_VIEWS=500
# OBSERVE_LOWCTR_CTR_THRESHOLD=0.02
# OBSERVE_HIGHCPC_CPC_THRESHOLD=15
# OBSERVE_LOWSIMILARITY_SIMILARITY_THRESHOLD=50
# OBSERVE_LOWSIMILARITY_COUNT_THRESHOLD=2
# OPTIMIZE_LOWPERF_MAX_CLICKS=5
# OPTIMIZE_LOWPERF_SIMILARITY_THRESHOLD=40
# OPTIMIZE_LOWPERF_COUNT_THRESHOLD=0

# Redis配置（如果使用独立Redis）
# REDIS_URL=redis://redis:6379/0
# REDIS_URL=redis://*************:6379/0
