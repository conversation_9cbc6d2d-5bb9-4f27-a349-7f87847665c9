# 关键词分析微服务部署脚本 (PowerShell版本)
# 使用方法: .\scripts\deploy.ps1 [start|stop|restart|status|logs]

param(
    [Parameter(Position=0)]
    [ValidateSet("start", "stop", "restart", "status", "logs", "verify")]
    [string]$Action = "start"
)

$COMPOSE_FILE = "docker-compose.business.yml"
$SERVICE_NAME = "keyword-analysis-service"

# 日志函数
function Write-Info {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# 检查前置条件
function Test-Prerequisites {
    Write-Info "检查前置条件..."
    
    # 检查Docker
    if (-not (Get-Command docker -ErrorAction SilentlyContinue)) {
        Write-Error "Docker未安装或不在PATH中"
        exit 1
    }
    
    # 检查Docker Compose
    if (-not (Get-Command docker-compose -ErrorAction SilentlyContinue)) {
        Write-Error "Docker Compose未安装或不在PATH中"
        exit 1
    }
    
    # 检查配置文件
    if (-not (Test-Path ".env")) {
        Write-Warning ".env文件不存在，请从.env.business.example复制并配置"
        if (Test-Path ".env.business.example") {
            Copy-Item ".env.business.example" ".env"
            Write-Info "已复制.env.business.example到.env，请编辑配置"
        }
        exit 1
    }
    
    # 检查基础设施网络
    $networks = docker network ls --format "{{.Name}}"
    if ($networks -notcontains "microservices") {
        Write-Warning "microservices网络不存在，正在创建..."
        docker network create microservices
        Write-Success "microservices网络创建成功"
    }
    
    Write-Success "前置条件检查完成"
}

# 启动服务
function Start-Service {
    Write-Info "启动关键词分析微服务..."
    
    Test-Prerequisites
    
    # 构建并启动服务
    docker-compose -f $COMPOSE_FILE up -d --build
    
    # 等待服务启动
    Write-Info "等待服务启动..."
    Start-Sleep -Seconds 10
    
    # 检查服务状态
    $status = docker-compose -f $COMPOSE_FILE ps
    if ($status -match "Up") {
        Write-Success "服务启动成功"
        
        # 显示服务信息
        Show-ServiceInfo
        
        # 验证服务
        Test-Service
    } else {
        Write-Error "服务启动失败"
        docker-compose -f $COMPOSE_FILE logs $SERVICE_NAME
        exit 1
    }
}

# 停止服务
function Stop-Service {
    Write-Info "停止关键词分析微服务..."
    docker-compose -f $COMPOSE_FILE down
    Write-Success "服务已停止"
}

# 重启服务
function Restart-Service {
    Write-Info "重启关键词分析微服务..."
    Stop-Service
    Start-Sleep -Seconds 2
    Start-Service
}

# 显示服务状态
function Show-Status {
    Write-Info "服务状态:"
    docker-compose -f $COMPOSE_FILE ps
    
    Write-Host ""
    Write-Info "容器资源使用情况:"
    $containers = docker-compose -f $COMPOSE_FILE ps -q
    if ($containers) {
        docker stats --no-stream $containers
    }
}

# 显示服务日志
function Show-Logs {
    Write-Info "显示服务日志 (Ctrl+C退出):"
    docker-compose -f $COMPOSE_FILE logs -f $SERVICE_NAME
}

# 显示服务信息
function Show-ServiceInfo {
    Write-Host ""
    Write-Info "=== 服务信息 ==="
    Write-Host "服务名称: $SERVICE_NAME"
    Write-Host "容器名称: ${SERVICE_NAME}-server"
    Write-Host "服务端口: 8000"
    Write-Host "健康检查: http://localhost:8000/api/v1/health"
    Write-Host "服务信息: http://localhost:8000/api/v1/info"
    Write-Host "API文档: http://localhost:8000/docs"
    Write-Host ""
}

# 验证服务
function Test-Service {
    Write-Info "验证服务部署..."
    
    # 等待服务完全启动
    Start-Sleep -Seconds 5
    
    # 检查健康检查端点
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:8000/api/v1/health" -UseBasicParsing -TimeoutSec 10
        if ($response.StatusCode -eq 200) {
            Write-Success "✓ 健康检查端点正常"
        } else {
            Write-Warning "✗ 健康检查端点异常"
        }
    } catch {
        Write-Warning "✗ 健康检查端点异常: $($_.Exception.Message)"
    }
    
    # 检查Consul注册（如果Consul可用）
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:8500/v1/catalog/service/$SERVICE_NAME" -UseBasicParsing -TimeoutSec 5
        if ($response.StatusCode -eq 200) {
            Write-Success "✓ 服务已注册到Consul"
        } else {
            Write-Warning "✗ 服务未注册到Consul"
        }
    } catch {
        Write-Warning "✗ 服务未注册到Consul或Consul不可用"
    }
    
    Write-Host ""
    Write-Info "=== 验证命令 ==="
    Write-Host "健康检查: curl http://localhost:8000/api/v1/health"
    Write-Host "服务信息: curl http://localhost:8000/api/v1/info"
    Write-Host "Consul服务: curl http://localhost:8500/v1/catalog/service/$SERVICE_NAME"
    Write-Host "网关访问: curl http://localhost/api/$SERVICE_NAME/api/v1/health"
    Write-Host ""
}

# 主函数
switch ($Action) {
    "start" { Start-Service }
    "stop" { Stop-Service }
    "restart" { Restart-Service }
    "status" { Show-Status }
    "logs" { Show-Logs }
    "verify" { Test-Service }
    default {
        Write-Host "使用方法: .\scripts\deploy.ps1 [start|stop|restart|status|logs|verify]"
        Write-Host ""
        Write-Host "命令说明:"
        Write-Host "  start   - 启动服务 (默认)"
        Write-Host "  stop    - 停止服务"
        Write-Host "  restart - 重启服务"
        Write-Host "  status  - 显示服务状态"
        Write-Host "  logs    - 显示服务日志"
        Write-Host "  verify  - 验证服务部署"
        exit 1
    }
}
