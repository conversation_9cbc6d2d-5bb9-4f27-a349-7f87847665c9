#!/bin/bash

# 关键词分析微服务部署脚本
# 使用方法: ./scripts/deploy.sh [start|stop|restart|status|logs]

set -e

COMPOSE_FILE="docker-compose.business.yml"
SERVICE_NAME="keyword-analysis-service"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查前置条件
check_prerequisites() {
    log_info "检查前置条件..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装或不在PATH中"
        exit 1
    fi
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装或不在PATH中"
        exit 1
    fi
    
    # 检查配置文件
    if [ ! -f ".env" ]; then
        log_warning ".env文件不存在，请从.env.business.example复制并配置"
        if [ -f ".env.business.example" ]; then
            cp .env.business.example .env
            log_info "已复制.env.business.example到.env，请编辑配置"
        fi
        exit 1
    fi
    
    # 检查基础设施网络
    if ! docker network ls | grep -q "microservices"; then
        log_warning "microservices网络不存在，正在创建..."
        docker network create microservices
        log_success "microservices网络创建成功"
    fi
    
    log_success "前置条件检查完成"
}

# 启动服务
start_service() {
    log_info "启动关键词分析微服务..."
    
    check_prerequisites
    
    # 构建并启动服务
    docker-compose -f $COMPOSE_FILE up -d --build
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 10
    
    # 检查服务状态
    if docker-compose -f $COMPOSE_FILE ps | grep -q "Up"; then
        log_success "服务启动成功"
        
        # 显示服务信息
        show_service_info
        
        # 验证服务
        verify_service
    else
        log_error "服务启动失败"
        docker-compose -f $COMPOSE_FILE logs $SERVICE_NAME
        exit 1
    fi
}

# 停止服务
stop_service() {
    log_info "停止关键词分析微服务..."
    docker-compose -f $COMPOSE_FILE down
    log_success "服务已停止"
}

# 重启服务
restart_service() {
    log_info "重启关键词分析微服务..."
    stop_service
    sleep 2
    start_service
}

# 显示服务状态
show_status() {
    log_info "服务状态:"
    docker-compose -f $COMPOSE_FILE ps
    
    echo ""
    log_info "容器资源使用情况:"
    docker stats --no-stream $(docker-compose -f $COMPOSE_FILE ps -q) 2>/dev/null || true
}

# 显示服务日志
show_logs() {
    log_info "显示服务日志 (Ctrl+C退出):"
    docker-compose -f $COMPOSE_FILE logs -f $SERVICE_NAME
}

# 显示服务信息
show_service_info() {
    echo ""
    log_info "=== 服务信息 ==="
    echo "服务名称: $SERVICE_NAME"
    echo "容器名称: ${SERVICE_NAME}-server"
    echo "服务端口: 8000"
    echo "健康检查: http://localhost:8000/api/v1/health"
    echo "服务信息: http://localhost:8000/api/v1/info"
    echo "API文档: http://localhost:8000/docs"
    echo ""
}

# 验证服务
verify_service() {
    log_info "验证服务部署..."
    
    # 等待服务完全启动
    sleep 5
    
    # 检查健康检查端点
    if curl -s -f http://localhost:8000/api/v1/health > /dev/null; then
        log_success "✓ 健康检查端点正常"
    else
        log_warning "✗ 健康检查端点异常"
    fi
    
    # 检查Consul注册（如果Consul可用）
    if curl -s -f http://localhost:8500/v1/catalog/service/$SERVICE_NAME > /dev/null 2>&1; then
        log_success "✓ 服务已注册到Consul"
    else
        log_warning "✗ 服务未注册到Consul或Consul不可用"
    fi
    
    echo ""
    log_info "=== 验证命令 ==="
    echo "健康检查: curl http://localhost:8000/api/v1/health"
    echo "服务信息: curl http://localhost:8000/api/v1/info"
    echo "Consul服务: curl http://localhost:8500/v1/catalog/service/$SERVICE_NAME"
    echo "网关访问: curl http://localhost/api/$SERVICE_NAME/api/v1/health"
    echo ""
}

# 主函数
main() {
    case "${1:-start}" in
        start)
            start_service
            ;;
        stop)
            stop_service
            ;;
        restart)
            restart_service
            ;;
        status)
            show_status
            ;;
        logs)
            show_logs
            ;;
        verify)
            verify_service
            ;;
        *)
            echo "使用方法: $0 [start|stop|restart|status|logs|verify]"
            echo ""
            echo "命令说明:"
            echo "  start   - 启动服务 (默认)"
            echo "  stop    - 停止服务"
            echo "  restart - 重启服务"
            echo "  status  - 显示服务状态"
            echo "  logs    - 显示服务日志"
            echo "  verify  - 验证服务部署"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
