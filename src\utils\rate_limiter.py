"""
API速率限制器
"""
import time
from typing import Dict, Optional
from dataclasses import dataclass
from loguru import logger


@dataclass
class RateLimit:
    """速率限制配置"""
    requests_per_minute: int  # 每分钟请求数
    min_interval: float       # 最小间隔（秒）
    burst_limit: int         # 突发请求限制
    
    @property
    def interval_seconds(self) -> float:
        """计算请求间隔（秒）"""
        return 60.0 / self.requests_per_minute


class APIRateLimiter:
    """API速率限制器"""
    
    # 预定义的API限制配置
    RATE_LIMITS = {
        # 广告API - 相对宽松
        'adv_api': RateLimit(
            requests_per_minute=30,
            min_interval=2.0,
            burst_limit=5
        ),
        
        # 分析API - 一般限制
        'analytics_api': RateLimit(
            requests_per_minute=20,
            min_interval=3.0,
            burst_limit=3
        ),
        
        # 搜索文本API - 严格限制
        'search_texts_api': RateLimit(
            requests_per_minute=3,
            min_interval=20.0,
            burst_limit=3
        ),
        
        # 默认限制
        'default': RateLimit(
            requests_per_minute=15,
            min_interval=4.0,
            burst_limit=3
        )
    }
    
    def __init__(self):
        self.last_request_times: Dict[str, float] = {}
        self.request_counts: Dict[str, int] = {}
        self.minute_start_times: Dict[str, float] = {}
    
    def wait_if_needed(self, api_type: str = 'default', endpoint: Optional[str] = None) -> None:
        """
        根据API类型等待适当的时间
        
        Args:
            api_type: API类型 ('adv_api', 'analytics_api', 'search_texts_api', 'default')
            endpoint: 具体的API端点（用于日志记录）
        """
        rate_limit = self.RATE_LIMITS.get(api_type, self.RATE_LIMITS['default'])
        current_time = time.time()
        
        # 检查是否需要重置分钟计数器
        self._reset_minute_counter_if_needed(api_type, current_time)
        
        # 检查每分钟请求限制
        if self._is_minute_limit_exceeded(api_type, rate_limit):
            wait_time = 60.0 - (current_time - self.minute_start_times[api_type])
            if wait_time > 0:
                logger.warning(f"API {api_type} 达到每分钟请求限制，等待 {wait_time:.1f} 秒")
                time.sleep(wait_time)
                self._reset_minute_counter_if_needed(api_type, time.time())
        
        # 检查最小间隔限制
        if api_type in self.last_request_times:
            elapsed = current_time - self.last_request_times[api_type]
            min_interval = rate_limit.min_interval
            
            if elapsed < min_interval:
                wait_time = min_interval - elapsed
                logger.debug(f"API {api_type} 等待间隔限制: {wait_time:.1f} 秒")
                time.sleep(wait_time)
        
        # 更新请求记录
        self.last_request_times[api_type] = time.time()
        self.request_counts[api_type] = self.request_counts.get(api_type, 0) + 1
        
        if endpoint:
            logger.debug(f"API请求: {api_type} - {endpoint}")
    
    def _reset_minute_counter_if_needed(self, api_type: str, current_time: float) -> None:
        """重置分钟计数器（如果需要）"""
        if api_type not in self.minute_start_times:
            self.minute_start_times[api_type] = current_time
            self.request_counts[api_type] = 0
        elif current_time - self.minute_start_times[api_type] >= 60.0:
            self.minute_start_times[api_type] = current_time
            self.request_counts[api_type] = 0
    
    def _is_minute_limit_exceeded(self, api_type: str, rate_limit: RateLimit) -> bool:
        """检查是否超过每分钟请求限制"""
        current_count = self.request_counts.get(api_type, 0)
        return current_count >= rate_limit.requests_per_minute
    
    def get_api_type_for_url(self, url: str) -> str:
        """根据URL确定API类型"""
        if 'advert-api.wildberries.ru' in url:
            return 'adv_api'
        elif 'seller-analytics-api.wildberries.ru' in url:
            if 'search-report/product/search-texts' in url:
                return 'search_texts_api'
            else:
                return 'analytics_api'
        else:
            return 'default'
    
    def get_stats(self) -> Dict[str, Dict[str, any]]:
        """获取速率限制统计信息"""
        stats = {}
        current_time = time.time()
        
        for api_type in self.RATE_LIMITS.keys():
            if api_type in self.request_counts:
                last_request = self.last_request_times.get(api_type, 0)
                minute_start = self.minute_start_times.get(api_type, 0)
                
                stats[api_type] = {
                    'total_requests': self.request_counts[api_type],
                    'requests_this_minute': self.request_counts[api_type] if current_time - minute_start < 60 else 0,
                    'last_request_ago': current_time - last_request if last_request > 0 else None,
                    'rate_limit': self.RATE_LIMITS[api_type]
                }
        
        return stats


# 全局速率限制器实例
rate_limiter = APIRateLimiter()
