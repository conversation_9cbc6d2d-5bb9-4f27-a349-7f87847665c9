"""
服务启动脚本
"""
import os
import sys
import asyncio
import uvicorn
from loguru import logger

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.api.app import app
from src.services.consul_service import graceful_shutdown


async def main():
    """主函数"""
    # 设置信号处理器
    graceful_shutdown.setup_signal_handlers()
    
    # 获取配置
    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("SERVICE_PORT", "8000"))
    
    logger.info(f"启动关键词分析服务...")
    logger.info(f"服务地址: {host}:{port}")
    logger.info(f"服务名称: {os.getenv('SERVICE_NAME', 'keyword-analysis-service')}")
    logger.info(f"环境: {os.getenv('ENVIRONMENT', 'production')}")
    
    # 启动服务
    config = uvicorn.Config(
        app=app,
        host=host,
        port=port,
        log_level="info",
        access_log=True
    )
    
    server = uvicorn.Server(config)
    
    try:
        await server.serve()
    except KeyboardInterrupt:
        logger.info("收到中断信号，开始关闭服务...")
    finally:
        # 执行优雅关闭
        await graceful_shutdown.shutdown()


if __name__ == "__main__":
    asyncio.run(main())
