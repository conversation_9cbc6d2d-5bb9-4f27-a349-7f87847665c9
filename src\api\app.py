"""
FastAPI应用主文件
"""
import os
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager

from .routes import router
from ..services.consul_service import ConsulService


# 全局Consul服务实例
consul_service = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global consul_service
    
    # 启动时注册服务到Consul
    try:
        consul_service = ConsulService()
        await consul_service.register_service()
    except Exception as e:
        print(f"Consul服务注册失败: {e}")
    
    yield
    
    # 关闭时注销服务
    try:
        if consul_service:
            await consul_service.deregister_service()
    except Exception as e:
        print(f"Consul服务注销失败: {e}")


def create_app() -> FastAPI:
    """创建FastAPI应用"""
    app = FastAPI(
        title="Wildberries关键词分析服务",
        description="通过广告ID查询关键词排除情况的微服务",
        version="1.0.0",
        lifespan=lifespan
    )
    
    # 添加CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # 注册路由
    app.include_router(router, prefix="/api/v1")
    
    return app


# 创建应用实例
app = create_app()


if __name__ == "__main__":
    import uvicorn
    
    port = int(os.getenv("SERVICE_PORT", "8000"))
    uvicorn.run(
        "src.api.app:app",
        host="0.0.0.0",
        port=port,
        reload=False
    )
