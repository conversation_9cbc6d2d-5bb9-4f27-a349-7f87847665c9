# Wildberries关键词分析微服务

## 📋 项目概述

这是一个基于FastAPI的微服务，提供Wildberries广告关键词分析功能。服务支持通过广告ID查询关键词排除情况，并自动注册到Consul服务发现系统。

## 🏗️ 项目结构

```
wb_adv/
├── src/                          # 源代码目录
│   ├── api/                      # API层
│   │   ├── app.py               # FastAPI应用
│   │   ├── routes.py            # API路由
│   │   └── models.py            # API响应模型
│   ├── core/                     # 核心模块
│   │   ├── config.py            # 配置管理
│   │   └── database.py          # 数据库连接
│   ├── models/                   # 数据模型
│   │   ├── campaign.py          # 广告活动模型
│   │   ├── keyword.py           # 关键词模型
│   │   ├── product.py           # 产品模型
│   │   ├── similarity.py        # 相似度模型
│   │   └── result.py            # 结果模型
│   ├── services/                 # 服务层
│   │   ├── consul_service.py    # Consul服务注册
│   │   ├── wb_api_client.py     # Wildberries API客户端
│   │   └── data_processor.py    # 数据处理器
│   └── utils/                    # 工具模块
│       ├── logger.py            # 日志配置
│       └── rate_limiter.py      # 速率限制
├── deployment/                   # 部署相关文件
├── docs/                        # 文档
│   └── DEPLOYMENT_GUIDE.md      # 部署指南
├── scripts/                     # 脚本
│   ├── deploy.sh               # Linux/Mac部署脚本
│   └── deploy.ps1              # Windows部署脚本
├── tests/                       # 测试文件
├── docker-compose.business.yml  # 微服务部署配置
├── Dockerfile                   # Docker镜像构建
├── run_service.py              # 服务启动脚本
├── requirements.txt            # Python依赖
└── .env.business.example       # 环境变量模板
```

## 🚀 快速开始

### 1. 环境准备

确保已安装以下软件：
- Docker & Docker Compose
- 基础设施服务（Consul、PostgreSQL等）

### 2. 配置环境变量

```bash
# 复制环境变量模板
cp .env.business.example .env

# 编辑配置文件
nano .env
```

关键配置项：
```bash
# 数据库连接
PG_HOST=postgres
PG_USER=your_db_user
PG_PASSWORD=your_db_password
PG_DB=your_database

# Wildberries API
WB_API_KEYS=your_api_key_1,your_api_key_2

# Consul配置
CONSUL_HOST=consul
```

### 3. 部署服务

#### 使用部署脚本（推荐）

**Linux/Mac:**
```bash
./scripts/deploy.sh start
```

**Windows:**
```powershell
.\scripts\deploy.ps1 start
```

#### 手动部署

```bash
# 启动服务
docker-compose -f docker-compose.business.yml up -d

# 查看状态
docker-compose -f docker-compose.business.yml ps

# 查看日志
docker-compose -f docker-compose.business.yml logs -f keyword-analysis-service
```

### 4. 验证部署

```bash
# 健康检查
curl http://localhost:8000/api/v1/health

# 服务信息
curl http://localhost:8000/api/v1/info

# 检查Consul注册
curl http://localhost:8500/v1/catalog/service/keyword-analysis-service

# 通过网关访问（如果配置了网关）
curl http://localhost/api/keyword-analysis-service/api/v1/health
```

## 📡 API接口

### 基础接口

- `GET /api/v1/health` - 健康检查
- `GET /api/v1/info` - 服务信息
- `GET /docs` - API文档（Swagger UI）

### 业务接口

#### 查询广告关键词分析

```http
GET /api/v1/campaign/{campaign_id}/keywords
```

**参数:**
- `campaign_id` (int): 广告活动ID

**响应示例:**
```json
{
  "campaign_id": 26771183,
  "total_keywords": 1009,
  "keywords": [
    {
      "campaign_id": 26771183,
      "nm_id": 253486273,
      "keyword": "люстра на потолок",
      "avg_similarity": 60.0,
      "similar_count": 5,
      "competitor_count": 1,
      "views": 106729,
      "sum": 35186.0,
      "clicks": 3985,
      "ctr": 0.04,
      "cpc": 8.83,
      "count": 107576,
      "order_generating": false,
      "exclude_irrelevant": false,
      "exclude_zero_click": false,
      "exclude_no_data": false,
      "observe_low_ctr": false,
      "observe_high_cpc": false,
      "observe_low_similarity": false,
      "optimize_low_perf": false,
      "keep": true,
      "should_exclude": false
    }
  ],
  "summary": {
    "total_keywords": 1009,
    "order_generating_keywords": 156,
    "keywords_to_exclude": 234,
    "keywords_to_keep": 775,
    "exclude_categories": {
      "irrelevant": 45,
      "zero_click": 123,
      "no_data": 66
    },
    "observe_categories": {
      "low_ctr": 89,
      "high_cpc": 34,
      "low_similarity": 78
    },
    "optimize_categories": {
      "low_performance": 67
    },
    "percentages": {
      "order_generating": 15.46,
      "to_exclude": 23.2,
      "to_keep": 76.8
    }
  }
}
```

## 🔧 开发指南

### 本地开发

```bash
# 安装依赖
pip install -r requirements.txt

# 设置环境变量
export PYTHONPATH=$PWD/src

# 启动开发服务器
python run_service.py
```

### 测试

```bash
# 运行测试
python -m pytest tests/

# 测试特定功能
python test_analysis.py
```

### 代码结构说明

- **API层** (`src/api/`): FastAPI应用和路由定义
- **核心层** (`src/core/`): 配置管理和数据库连接
- **模型层** (`src/models/`): 数据模型定义
- **服务层** (`src/services/`): 业务逻辑和外部服务集成
- **工具层** (`src/utils/`): 通用工具和辅助函数

## 🐳 Docker部署

### 构建镜像

```bash
docker build -t keyword-analysis-service .
```

### 运行容器

```bash
docker run -d \
  --name keyword-analysis-service \
  --network microservices \
  -p 8000:8000 \
  --env-file .env \
  keyword-analysis-service
```

## 📊 监控和维护

### 日志管理

```bash
# 查看实时日志
docker-compose -f docker-compose.business.yml logs -f keyword-analysis-service

# 查看最近日志
docker-compose -f docker-compose.business.yml logs --tail=100 keyword-analysis-service
```

### 服务扩展

```bash
# 扩展到3个实例
docker-compose -f docker-compose.business.yml up -d --scale keyword-analysis-service=3
```

### 健康监控

服务提供以下监控端点：
- `/api/v1/health` - 基础健康检查
- `/api/v1/info` - 服务信息和版本

## 🔐 安全配置

- 容器以非root用户运行
- 启用安全选项 `no-new-privileges`
- 资源限制防止资源耗尽
- 敏感信息通过环境变量管理

## 📚 相关文档

- [部署指南](docs/DEPLOYMENT_GUIDE.md)
- [业务服务集成指南](BUSINESS_SERVICE_INTEGRATION_GUIDE.md)
- [API文档](http://localhost:8000/docs) (服务运行时可访问)

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持

如有问题或需要帮助，请：
1. 查看 [部署指南](docs/DEPLOYMENT_GUIDE.md)
2. 检查 [故障排除](docs/DEPLOYMENT_GUIDE.md#故障排除) 部分
3. 提交 Issue 或联系维护团队

---

**版本**: 1.0.0  
**维护者**: 开发团队  
**最后更新**: 2024-01-XX
