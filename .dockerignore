# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
logs/
*.log

# Test files
test_*.py
*_test.py
tests/

# Documentation
docs/
*.md
!README.md

# Git
.git/
.gitignore

# Data files
*.csv
*.xlsx
*.json
!requirements.txt

# Temporary files
~$*
*.tmp
*.temp

# Docker
Dockerfile*
docker-compose*.yml
.dockerignore
