"""
Wildberries API客户端
"""
import requests
from typing import List, Dict, Any
from datetime import datetime, timedelta
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type
from loguru import logger

from ..core.config import settings
from ..models.campaign import Campaign
from ..models.keyword import Keyword
from ..models.product import Product
from ..utils.rate_limiter import rate_limiter


class WildberriesAPIError(Exception):
    """Wildberries API异常"""
    pass


class WildberriesAPIClient:
    """Wildberries API客户端"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.adv_base_url = settings.wb_adv_api_base_url
        self.analytics_base_url = settings.wb_analytics_api_base_url
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': api_key,
            'Content-Type': 'application/json'
        })
    
    def _make_request(self, method: str, url: str, **kwargs) -> Dict[str, Any]:
        """发起API请求"""
        try:
            # 使用智能速率限制器
            api_type = rate_limiter.get_api_type_for_url(url)
            endpoint = url.split('/')[-1] if '/' in url else url
            rate_limiter.wait_if_needed(api_type, endpoint)

            response = self.session.request(
                method=method,
                url=url,
                timeout=settings.request_timeout,
                **kwargs
            )
            response.raise_for_status()

            return response.json()
        except requests.exceptions.RequestException as e:
            logger.error(f"API请求失败: {url}, 错误: {e}")
            raise WildberriesAPIError(f"API请求失败: {e}")
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type(WildberriesAPIError)
    )
    def get_campaigns(self) -> List[Campaign]:
        """获取所有广告活动"""
        url = f"{self.adv_base_url}/adv/v1/promotion/count"

        logger.info("获取广告活动列表")
        data = self._make_request('GET', url)

        logger.debug(f"API返回数据类型: {type(data)}, 内容: {data}")

        campaigns = []

        # 检查返回数据格式
        if isinstance(data, list):
            items = data
        elif isinstance(data, dict) and 'adverts' in data:
            items = data['adverts']
        elif isinstance(data, dict) and 'data' in data:
            items = data['data']
        else:
            logger.warning(f"未知的API返回格式: {data}")
            return campaigns

        for item in items:
            try:
                # 确保 item 是字典类型
                if not isinstance(item, dict):
                    continue

                # 处理不同的数据结构
                if 'advert_list' in item:
                    # 这是一个分组，包含多个广告
                    for advert in item['advert_list']:
                        # 确保 advert 是字典类型
                        if isinstance(advert, dict):
                            campaign = Campaign(
                                campaign_id=advert.get('advertId', 0),
                                name=advert.get('name', ''),
                                type=item.get('type', 0),
                                status=item.get('status', 0),
                                create_time=datetime.fromisoformat(advert.get('changeTime', '').replace('Z', '+00:00')),
                                change_time=datetime.fromisoformat(advert.get('changeTime', '').replace('Z', '+00:00'))
                            )
                            campaigns.append(campaign)
                else:
                    # 这是单个广告
                    campaign = Campaign(
                        campaign_id=item.get('advertId', 0),
                        name=item.get('name', ''),
                        type=item.get('type', 0),
                        status=item.get('status', 0),
                        create_time=datetime.fromisoformat(item.get('createTime', '').replace('Z', '+00:00')),
                        change_time=datetime.fromisoformat(item.get('changeTime', '').replace('Z', '+00:00'))
                    )
                    campaigns.append(campaign)
            except Exception as e:
                logger.error(f"解析广告活动数据失败: {item}, 错误: {e}")
                continue

        logger.info(f"获取到 {len(campaigns)} 个广告活动")
        return campaigns
    
    def filter_auction_campaigns(self, campaigns: List[Campaign]) -> List[Campaign]:
        """筛选拍卖广告（type=9, status=9）"""
        filtered = [c for c in campaigns if c.type == 9 and c.status == 9]
        logger.info(f"筛选出 {len(filtered)} 个拍卖广告")
        return filtered
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type(WildberriesAPIError)
    )
    def get_campaign_keywords(self, campaign_id: int) -> List[Keyword]:
        """获取广告活动的关键词"""
        url = f"{self.adv_base_url}/adv/v1/stat/words"
        params = {'id': campaign_id}

        logger.info(f"获取广告活动 {campaign_id} 的关键词")
        try:
            data = self._make_request('GET', url, params=params)
            if data and 'words' in data and data['words'].get('keywords'):
                keywords_data = data['words']['keywords']
                logger.info(f"广告活动 {campaign_id} 有 {len(keywords_data)} 个关键词")

                # 转换为Keyword对象
                keywords = []
                for item in keywords_data:
                    if isinstance(item, dict):
                        keyword = Keyword(
                            keyword=item.get('keyword', ''),
                            count=item.get('count', 0)
                        )
                        keywords.append(keyword)
                return keywords
        except Exception as e:
            logger.warning(f"获取关键词失败: {e}")

        # 如果主要方法失败，尝试备用方法
        return self._try_alternative_keyword_endpoints(campaign_id)
        


    def _try_alternative_keyword_endpoints(self, campaign_id: int) -> List[Keyword]:
        """尝试其他关键词获取端点"""
        keywords = []

        # 尝试端点1: /adv/v1/promotion/adverts (可能包含关键词信息)
        try:
            url = f"{self.adv_base_url}/adv/v1/promotion/adverts"
            params = {'id': campaign_id}
            data = self._make_request('GET', url, params=params)
            logger.debug(f"备用端点1返回数据: {data}")

            # 检查是否有关键词信息
            if isinstance(data, dict) and 'keywords' in data:
                for item in data['keywords']:
                    if isinstance(item, dict) and 'keyword' in item:
                        keyword = Keyword(
                            keyword=item['keyword'],
                            count=item.get('count', 0)
                        )
                        keywords.append(keyword)
        except Exception as e:
            logger.debug(f"备用端点1失败: {e}")

        # 尝试端点2: /adv/v0/stats/keywords (统计端点可能包含关键词列表)
        if not keywords:
            try:
                from datetime import datetime, timedelta
                end_date = datetime.now()
                start_date = end_date - timedelta(days=7)

                url = f"{self.adv_base_url}/adv/v0/stats/keywords"
                payload = {
                    "id": campaign_id,
                    "dates": [start_date.strftime('%Y-%m-%d'), end_date.strftime('%Y-%m-%d')]
                }
                data = self._make_request('POST', url, json=payload)
                logger.debug(f"备用端点2返回数据: {data}")

                # 从统计数据中提取关键词
                if isinstance(data, list):
                    for item in data:
                        if isinstance(item, dict) and 'keywords' in item:
                            keywords_list = item.get('keywords', [])
                            if isinstance(keywords_list, list):
                                for kw_data in keywords_list:
                                    if isinstance(kw_data, dict) and 'keyword' in kw_data:
                                        keyword = Keyword(
                                            keyword=kw_data.get('keyword', ''),
                                            count=kw_data.get('count', 0)
                                        )
                                        keywords.append(keyword)

            except Exception as e:
                logger.debug(f"备用端点2失败: {e}")

        return keywords

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type(WildberriesAPIError)
    )
    def get_keyword_stats_period(self, campaign_id: int, start_date: str, end_date: str) -> List[Dict[str, Any]]:
        """获取指定时间段的关键词统计数据"""
        url = f"{self.adv_base_url}/adv/v0/stats/keywords"
        payload = {
            "advert_id": campaign_id,
            "from": start_date,
            "to": end_date
        }

        logger.info(f"获取广告活动 {campaign_id} 从 {start_date} 到 {end_date} 的关键词统计")
        data = self._make_request('GET', url, params=payload)

        # 确保返回正确的数据格式
        if isinstance(data, dict):
            if 'keywords' in data:
                # 如果API返回的是 {'keywords': [...]} 格式
                keywords_data = data['keywords']
                if isinstance(keywords_data, list):
                    return keywords_data
                else:
                    logger.warning(f"API返回的keywords字段不是列表格式: {type(keywords_data)}")
                    return []
            else:
                # 如果API返回的是其他格式，尝试直接返回
                logger.debug(f"API返回数据格式: {data}")
                return [data] if data else []
        elif isinstance(data, list):
            # 如果API直接返回列表
            return data
        else:
            logger.warning(f"API返回了未预期的数据格式: {type(data)}")
            return []

    def get_keyword_datas_monthly(self, campaign_id: int) -> Dict[str, Dict[str, Any]]:
        """获取最近一个月的关键词统计数据（分段获取）"""
        # 使用当天日期作为结束日期，确保包含当天数据
        end_date = datetime.now().date()-timedelta(days=1)
        start_date = end_date - timedelta(days=settings.data_period_days)

        all_stats = []
        current_start = start_date

        # 分7天一段获取数据
        while current_start <= end_date:
            current_end = min(current_start + timedelta(days=6), end_date)

            start_str = current_start.strftime('%Y-%m-%d')
            end_str = current_end.strftime('%Y-%m-%d')

            try:
                period_stats = self.get_keyword_stats_period(campaign_id, start_str, end_str)
                logger.debug(f"时间段 {start_str} 到 {end_str} 获取到 {len(period_stats)} 条数据")
                all_stats.extend(period_stats)
            except Exception as e:
                logger.warning(f"获取时间段 {start_str} 到 {end_str} 的数据失败: {e}")

            current_start = current_end + timedelta(days=1)

        logger.info(f"总共获取到 {len(all_stats)} 条原始统计数据")

        # 合并和计算统计数据
        return self._merge_keyword_stats(all_stats)

    def _merge_keyword_stats(self, stats_data: List[Dict[str, Any]]) -> Dict[str, Dict[str, Any]]:
        """合并关键词统计数据"""
        aggregated_data = {}
        for date_entry in stats_data:
            for stat in date_entry['stats']:
                keyword = stat['keyword']
                if keyword not in aggregated_data:
                    aggregated_data[keyword] = {
                        'clicks_list': [],
                        'sum_list': [],
                        'views_list': [],
                        'ctr_list': []
                    }
                aggregated_data[keyword]['clicks_list'].append(stat['clicks'])
                aggregated_data[keyword]['sum_list'].append(stat['sum'])
                aggregated_data[keyword]['views_list'].append(stat['views'])
                aggregated_data[keyword]['ctr_list'].append(stat['ctr'])

        for keyword, data in aggregated_data.items():
            data['clicks'] = sum(data['clicks_list'])
            data['sum'] = sum(data['sum_list'])
            data['views'] = sum(data['views_list'])
            # Для CTR вычисляем среднее значение, избегая деления на ноль
            if data['ctr_list']:
                data['ctr'] = sum(data['ctr_list']) / len(data['ctr_list'])
            else:
                data['ctr'] = 0

        return aggregated_data

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type(WildberriesAPIError)
    )
    def get_campaign_products(self, campaign_id: int) -> List[Product]:
        """获取广告活动的产品列表"""
        url = f"{self.adv_base_url}/adv/v1/promotion/adverts"
        payload = [campaign_id]  # 使用数组格式

        logger.info(f"获取广告活动 {campaign_id} 的产品列表")
        data = self._make_request('POST', url, json=payload)

        products = []
        if data and isinstance(data, list) and len(data) > 0:
            first_item = data[0]
            if isinstance(first_item, dict) and 'auction_multibids' in first_item:
                for item in first_item['auction_multibids']:
                    # 假设item是一个字典，包含nm_id和bid
                    if isinstance(item, dict):
                        product = Product(
                            nm_id=item.get('nm', 0),
                            bid=item.get('bid', 0)
                        )
                    products.append(product)

        logger.info(f"广告活动 {campaign_id} 获取到 {len(products)} 个产品")
        return products

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=20, max=30),  # 根据API限制调整等待时间
        retry=retry_if_exception_type(WildberriesAPIError)
    )
    def get_search_texts_by_products(self, nm_ids: List[int]) -> List[str]:
        """
        通过产品ID获取搜索文本

        Args:
            nm_ids: 产品ID列表

        Returns:
            去重后的搜索文本列表
        """
        if not nm_ids:
            logger.warning("产品ID列表为空")
            return []

        # 计算日期范围
        end_date = datetime.now().date() - timedelta(days=1)  # 昨天
        start_date = end_date - timedelta(days=30)  # 一个月前

        # 过去时期（用于对比）
        past_end_date = start_date - timedelta(days=1)
        past_start_date = past_end_date - timedelta(days=30)

        url = f"{self.analytics_base_url}/api/v2/search-report/product/search-texts"
        payload = {
            "currentPeriod": {
                "start": start_date.strftime('%Y-%m-%d'),
                "end": end_date.strftime('%Y-%m-%d')
            },
            "pastPeriod": {
                "start": past_start_date.strftime('%Y-%m-%d'),
                "end": past_end_date.strftime('%Y-%m-%d')
            },
            "nmIds": nm_ids,
            "topOrderBy": "orders",
            "includeSubstitutedSKUs": True,
            "includeSearchTexts": True,
            "orderBy": {
                "field": "orders",
                "mode": "desc"
            },
            "limit": 30
        }

        logger.info(f"获取产品 {nm_ids} 的搜索文本，日期范围: {start_date} 到 {end_date}")

        try:
            # 速率限制由rate_limiter统一管理，不需要额外等待
            data = self._make_request('POST', url, json=payload)

            search_texts = []
            if data and isinstance(data, dict) and 'data' in data:
                items = data['data'].get('items', [])
                for item in items:
                    if isinstance(item, dict) and 'text' in item:
                        text = item['text']
                        if text and text.strip():  # 确保文本不为空
                            search_texts.append(text.strip())

            # 去重
            unique_texts = list(set(search_texts))
            logger.info(f"获取到 {len(search_texts)} 个搜索文本，去重后 {len(unique_texts)} 个")

            return unique_texts

        except Exception as e:
            logger.error(f"获取搜索文本失败: {e}")
            return []


class MultiAccountAPIManager:
    """多账号API管理器"""

    def __init__(self):
        self.clients = []
        for api_key in settings.api_keys_list:
            client = WildberriesAPIClient(api_key)
            self.clients.append(client)

        logger.info(f"初始化了 {len(self.clients)} 个API客户端")

    def get_all_campaigns(self) -> List[Campaign]:
        """获取所有账号的广告活动"""
        all_campaigns = []

        for i, client in enumerate(self.clients):
            try:
                campaigns = client.get_campaigns()
                all_campaigns.extend(campaigns)
                logger.info(f"账号 {i+1} 获取到 {len(campaigns)} 个广告活动")
            except Exception as e:
                logger.error(f"账号 {i+1} 获取广告活动失败: {e}")

        return all_campaigns

    def process_campaigns_with_load_balancing(self, campaigns: List[Campaign]) -> List[Dict[str, Any]]:
        """使用负载均衡处理广告活动"""
        results = []

        # 将广告活动分配给不同的客户端
        for i, campaign in enumerate(campaigns):
            client = self.clients[i % len(self.clients)]

            try:
                # 获取关键词
                keywords = client.get_campaign_keywords(campaign.campaign_id)

                # 获取统计数据
                keyword_stats = client.get_keyword_stats_monthly(campaign.campaign_id)

                # 获取产品
                products = client.get_campaign_products(campaign.campaign_id)

                results.append({
                    'campaign': campaign,
                    'keywords': keywords,
                    'keyword_stats': keyword_stats,
                    'products': products
                })

            except Exception as e:
                logger.error(f"处理广告活动 {campaign.campaign_id} 失败: {e}")

        return results
