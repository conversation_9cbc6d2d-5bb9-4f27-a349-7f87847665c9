"""
分析结果模型
"""
from dataclasses import dataclass
from typing import Dict, Any


@dataclass
class FinalResult:
    """最终结果模型"""
    campaign_id: int
    nm_id: int
    keyword: str
    avg_similarity: float
    similar_count: int
    competitor_count: int
    valid_scores: int
    views: int
    sum: float
    clicks: int
    ctr: float
    count: int  # 关键词热度
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'campaign_id': self.campaign_id,
            'nm_id': self.nm_id,
            'keyword': self.keyword,
            'avg_similarity': self.avg_similarity,
            'similar_count': self.similar_count,
            'competitor_count': self.competitor_count,
            'valid_scores': self.valid_scores,
            'views': self.views,
            'sum': self.sum,
            'clicks': self.clicks,
            'ctr': self.ctr,
            'count': self.count
        }
