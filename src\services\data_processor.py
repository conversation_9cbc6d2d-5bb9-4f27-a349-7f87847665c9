"""
数据处理和分析模块
"""
import pandas as pd
from typing import List, Dict, Any
from loguru import logger

from ..models.campaign import Campaign
from ..models.similarity import SimilarityData
from ..models.result import FinalResult
from .wb_api_client import WildberriesAPIClient
from ..core.database import db_manager
from ..core.config import settings


class KeywordAnalyzer:
    """关键词分析器"""
    
    def __init__(self, api_client: WildberriesAPIClient):
        self.api_client = api_client
    
    def analyze_campaign(self, campaign: Campaign):
        """分析单个广告活动"""
        logger.info(f"开始分析广告活动 {campaign.campaign_id}")

        try:
            # 1. 获取关键词
            keywords = self.api_client.get_campaign_keywords(campaign.campaign_id)
            if not keywords:
                logger.warning(f"广告活动 {campaign.campaign_id} 没有关键词")
                return pd.DataFrame()

            logger.info(f"获取到 {len(keywords)} 个关键词")

            # 2. 获取关键词统计数据
            keyword_stats_dict = self.api_client.get_keyword_datas_monthly(campaign.campaign_id)
            logger.info(f"获取到 {len(keyword_stats_dict)} 个关键词的统计数据")

            # 3. 获取产品列表（保留此功能）
            products = self.api_client.get_campaign_products(campaign.campaign_id)
            if not products:
                logger.warning(f"广告活动 {campaign.campaign_id} 没有产品")
                return pd.DataFrame()

            logger.info(f"获取到 {len(products)} 个产品")

            # 4. 获取产品的搜索文本
            nm_ids = [product.nm_id for product in products]
            search_texts = self.api_client.get_search_texts_by_products(nm_ids)
            logger.info(f"从API获取到 {len(search_texts)} 个搜索文本")

            # 5. 批量获取数据库中相似度数据（使用批量查询优化性能）
            keyword_list = [keyword.keyword for keyword in keywords]
            keyword_similarity_dict = db_manager.batch_get_similarity_data_by_keywords(keyword_list)
            logger.info(f"从数据库批量获取到 {len(keyword_similarity_dict)} 个关键词的相似度数据")

            # 6. 使用pandas进行数据拼接
            return self._merge_data_with_pandas_v2(campaign.campaign_id, keywords, keyword_stats_dict,
                                                 products, keyword_similarity_dict, search_texts)

        except Exception as e:
            logger.error(f"分析广告活动 {campaign.campaign_id} 失败: {e}")
            return pd.DataFrame()

    def _merge_data_with_pandas(self, campaign_id: int, keywords, keyword_stats_dict, products, similarity_data_dict):
        """使用pandas合并数据"""
        logger.info("开始使用pandas合并数据...")

        # 1. 创建关键词DataFrame
        keywords_data = []
        for keyword in keywords:
            keywords_data.append({
                'keyword': keyword.keyword,
                'count': keyword.count
            })
        df_keywords = pd.DataFrame(keywords_data)
        logger.info(f"关键词DataFrame: {len(df_keywords)} 行")

        # 2. 创建关键词统计DataFrame
        stats_data = []
        for keyword, stats in keyword_stats_dict.items():
            views = stats.get('views', 0)
            clicks = stats.get('clicks', 0)
            sum_value = stats.get('sum', 0.0)

            # 重新计算CTR：clicks除以views
            ctr = clicks / views if views > 0 else 0.0

            # 计算CPC：sum除以clicks
            cpc = sum_value / clicks if clicks > 0 else 0.0

            stats_data.append({
                'keyword': keyword,
                'views': views,
                'sum': sum_value,
                'clicks': clicks,
                'ctr': ctr,
                'cpc': cpc
            })
        df_stats = pd.DataFrame(stats_data)
        logger.info(f"统计数据DataFrame: {len(df_stats)} 行")

        # 3. 创建相似度DataFrame
        similarity_data = []
        for (keyword, nm_id), sim_data in similarity_data_dict.items():
            similarity_data.append({
                'keyword': keyword,
                'nm_id': nm_id,
                'avg_similarity': sim_data.avg_similarity,
                'similar_count': sim_data.similar_count,
                'competitor_count': sim_data.competitor_count,
                'valid_scores': sim_data.valid_scores
            })
        df_similarity = pd.DataFrame(similarity_data)
        logger.info(f"相似度DataFrame: {len(df_similarity)} 行")

        # 4. 创建产品DataFrame（为了完整性）
        products_data = []
        for product in products:
            products_data.append({
                'nm_id': product.nm_id
            })
        df_products = pd.DataFrame(products_data)
        logger.info(f"产品DataFrame: {len(df_products)} 行")

        # 5. 进行数据合并（类似Excel的VLOOKUP）
        # 首先合并关键词和统计数据
        df_merged = df_keywords.merge(df_stats, on='keyword', how='left')
        logger.info(f"合并关键词和统计数据后: {len(df_merged)} 行")

        # 创建关键词-产品的笛卡尔积
        df_keyword_product = df_merged.assign(key=1).merge(df_products.assign(key=1), on='key').drop('key', axis=1)
        logger.info(f"创建关键词-产品组合后: {len(df_keyword_product)} 行")

        # 合并相似度数据
        df_final = df_keyword_product.merge(df_similarity, on=['keyword', 'nm_id'], how='left')
        logger.info(f"合并相似度数据后: {len(df_final)} 行")

        # 6. 添加广告ID列
        df_final['campaign_id'] = campaign_id

        # 7. 重新排列列顺序，确保字段完整性
        expected_columns = ['campaign_id', 'nm_id', 'keyword', 'avg_similarity', 'similar_count',
                          'competitor_count', 'valid_scores', 'views', 'sum', 'clicks', 'ctr', 'cpc', 'count',
                          'Order_Generating', 'Exclude_Irrelevant', 'Exclude_ZeroClick', 'Exclude_NoData', 'Observe_LowCTR', 'Observe_HighCPC',
                          'Observe_LowSimilarity', 'Optimize_LowPerf', 'Keep', '是否排除标记']

        # 确保所有列都存在，如果不存在则填充空值
        for col in expected_columns:
            if col not in df_final.columns:
                df_final[col] = None

        # 选择并重新排序列
        df_final = df_final[expected_columns]

        # 统一数据类型
        logger.info("=== 统一数据类型 ===")

        # 将指定字段转换为整数（空值保持为NaN）
        integer_columns = ['avg_similarity', 'similar_count', 'competitor_count', 'valid_scores',
                          'views', 'clicks', 'count']

        for col in integer_columns:
            if col in df_final.columns:
                # 对于有空值的列，使用nullable integer类型
                if df_final[col].isna().any():
                    df_final[col] = df_final[col].astype('Int64')  # nullable integer
                else:
                    df_final[col] = df_final[col].astype('int64')
                logger.info(f"{col}: 转换为整数类型")

        # 特殊处理sum字段：先四舍五入再转换为整数
        if 'sum' in df_final.columns:
            if df_final['sum'].isna().any():
                df_final['sum'] = df_final['sum'].round().astype('Int64')  # nullable integer
            else:
                df_final['sum'] = df_final['sum'].round().astype('int64')
            logger.info("sum: 四舍五入后转换为整数类型")

        # 将ctr和cpc精确到2位小数
        if 'ctr' in df_final.columns:
            df_final['ctr'] = df_final['ctr'].round(2)
            logger.info("ctr: 精确到2位小数")

        if 'cpc' in df_final.columns:
            df_final['cpc'] = df_final['cpc'].round(2)
            logger.info("cpc: 精确到2位小数")

        # 8. 添加分析标记列
        logger.info("=== 添加分析标记列 ===")
        df_final = self._add_analysis_flags(df_final)

        logger.info(f"最终DataFrame: {len(df_final)} 行, {len(df_final.columns)} 列")
        logger.info(f"列名: {list(df_final.columns)}")

        # 检查关键字段的完整性
        key_fields = ['keyword', 'views', 'clicks', 'sum']
        for col in key_fields:
            if col in df_final.columns:
                null_count = df_final[col].isna().sum()
                if null_count > 0:
                    logger.warning(f"{col}: {null_count} 个空值")

        return df_final

    def _merge_data_with_pandas_v2(self, campaign_id: int, keywords, keyword_stats_dict, products, keyword_similarity_dict, search_texts):
        """使用pandas合并数据（新版本，支持关键词相似度数据和搜索文本）"""
        logger.info("开始使用pandas合并数据（v2版本）...")

        # 1. 创建关键词DataFrame
        keywords_data = []
        for keyword in keywords:
            keywords_data.append({
                'keyword': keyword.keyword,
                'count': keyword.count
            })
        df_keywords = pd.DataFrame(keywords_data)
        logger.info(f"关键词DataFrame: {len(df_keywords)} 行")

        # 2. 创建关键词统计DataFrame
        stats_data = []
        for keyword, stats in keyword_stats_dict.items():
            views = stats.get('views', 0)
            clicks = stats.get('clicks', 0)
            sum_value = stats.get('sum', 0.0)

            # 重新计算CTR：clicks除以views
            ctr = clicks / views if views > 0 else 0.0

            # 计算CPC：sum除以clicks
            cpc = sum_value / clicks if clicks > 0 else 0.0

            stats_data.append({
                'keyword': keyword,
                'views': views,
                'sum': sum_value,
                'clicks': clicks,
                'ctr': ctr,
                'cpc': cpc
            })
        df_stats = pd.DataFrame(stats_data)
        logger.info(f"统计数据DataFrame: {len(df_stats)} 行")

        # 3. 创建关键词相似度DataFrame
        similarity_data = []
        for keyword, sim_data in keyword_similarity_dict.items():
            similarity_data.append({
                'keyword': keyword,
                'avg_similarity': sim_data.avg_similarity,
                'similar_count': sim_data.similar_count,
                'competitor_count': sim_data.competitor_count,
                'valid_scores': sim_data.valid_scores
            })
        df_similarity = pd.DataFrame(similarity_data)
        logger.info(f"关键词相似度DataFrame: {len(df_similarity)} 行")

        # 4. 创建产品DataFrame
        products_data = []
        for product in products:
            products_data.append({
                'nm_id': product.nm_id
            })
        df_products = pd.DataFrame(products_data)
        logger.info(f"产品DataFrame: {len(df_products)} 行")

        # 5. 创建搜索文本DataFrame（用于记录）
        search_texts_data = []
        for text in search_texts:
            search_texts_data.append({
                'search_text': text
            })
        df_search_texts = pd.DataFrame(search_texts_data)
        logger.info(f"搜索文本DataFrame: {len(df_search_texts)} 行")

        # 6. 进行数据合并
        # 首先合并关键词和统计数据
        df_merged = df_keywords.merge(df_stats, on='keyword', how='left')
        logger.info(f"合并关键词和统计数据后: {len(df_merged)} 行")

        # 合并关键词相似度数据
        df_merged = df_merged.merge(df_similarity, on='keyword', how='left')
        logger.info(f"合并关键词相似度数据后: {len(df_merged)} 行")

        # 创建关键词-产品的笛卡尔积
        df_final = df_merged.assign(key=1).merge(df_products.assign(key=1), on='key').drop('key', axis=1)
        logger.info(f"创建关键词-产品组合后: {len(df_final)} 行")

        # 7. 添加广告ID列
        df_final['campaign_id'] = campaign_id

        # 8. 添加出单关键词标记
        logger.info("=== 添加出单关键词标记 ===")
        df_final['Order_Generating'] = df_final['keyword'].isin(search_texts)
        order_generating_count = df_final['Order_Generating'].sum()
        logger.info(f"出单关键词: {order_generating_count}/{len(df_final)} ({order_generating_count/len(df_final)*100:.1f}%)")

        # 9. 重新排列列顺序，确保字段完整性
        expected_columns = ['campaign_id', 'nm_id', 'keyword', 'avg_similarity', 'similar_count',
                          'competitor_count', 'valid_scores', 'views', 'sum', 'clicks', 'ctr', 'cpc', 'count',
                          'Order_Generating', 'Exclude_Irrelevant', 'Exclude_ZeroClick', 'Exclude_NoData', 'Observe_LowCTR', 'Observe_HighCPC',
                          'Observe_LowSimilarity', 'Optimize_LowPerf', 'Keep', '是否排除标记']

        # 确保所有列都存在，如果不存在则填充空值
        for col in expected_columns:
            if col not in df_final.columns:
                df_final[col] = None

        # 选择并重新排序列
        df_final = df_final[expected_columns]

        # 统一数据类型
        logger.info("=== 统一数据类型 ===")

        # 将指定字段转换为整数（空值保持为NaN）
        integer_columns = ['avg_similarity', 'similar_count', 'competitor_count', 'valid_scores',
                          'views', 'clicks', 'count']

        for col in integer_columns:
            if col in df_final.columns:
                # 对于有空值的列，使用nullable integer类型
                if df_final[col].isna().any():
                    df_final[col] = df_final[col].astype('Int64')  # nullable integer
                else:
                    df_final[col] = df_final[col].astype('int64')
                logger.info(f"{col}: 转换为整数类型")

        # 特殊处理sum字段：先四舍五入再转换为整数
        if 'sum' in df_final.columns:
            if df_final['sum'].isna().any():
                df_final['sum'] = df_final['sum'].round().astype('Int64')  # nullable integer
            else:
                df_final['sum'] = df_final['sum'].round().astype('int64')
            logger.info("sum: 四舍五入后转换为整数类型")

        # 将ctr和cpc精确到2位小数
        if 'ctr' in df_final.columns:
            df_final['ctr'] = df_final['ctr'].round(2)
            logger.info("ctr: 精确到2位小数")

        if 'cpc' in df_final.columns:
            df_final['cpc'] = df_final['cpc'].round(2)
            logger.info("cpc: 精确到2位小数")

        # 9. 添加分析标记列
        logger.info("=== 添加分析标记列 ===")
        df_final = self._add_analysis_flags(df_final)

        logger.info(f"最终DataFrame: {len(df_final)} 行, {len(df_final.columns)} 列")
        logger.info(f"列名: {list(df_final.columns)}")

        # 检查关键字段的完整性
        key_fields = ['keyword', 'views', 'clicks', 'sum']
        for col in key_fields:
            if col in df_final.columns:
                null_count = df_final[col].isna().sum()
                if null_count > 0:
                    logger.warning(f"{col}: {null_count} 个空值")

        # 记录搜索文本信息
        logger.info(f"获取到 {len(search_texts)} 个搜索文本")

        return df_final

    def analyze_multiple_campaigns(self, campaigns: List[Campaign]) -> List[FinalResult]:
        """分析多个广告活动"""
        all_results = []
        
        for i, campaign in enumerate(campaigns, 1):
            logger.info(f"处理第 {i}/{len(campaigns)} 个广告活动: {campaign.campaign_id}")
            
            try:
                results = self.analyze_campaign(campaign)
                all_results.extend(results)
            except Exception as e:
                logger.error(f"处理广告活动 {campaign.campaign_id} 时发生错误: {e}")
                continue
        
        logger.info(f"所有广告活动分析完成，总共生成 {len(all_results)} 条结果")
        return all_results

    def _add_analysis_flags(self, df: pd.DataFrame) -> pd.DataFrame:
        """添加分析标记列"""
        logger.info("开始计算分析标记...")

        # 计算平均CPC用于Observe_HighCPC判断（排除sum < 20的数据）
        high_cpc_filter = df['sum'].fillna(0) >= settings.observe_highcpc_min_sum
        filtered_cpc = df.loc[high_cpc_filter, 'cpc'] if 'cpc' in df.columns else pd.Series(dtype=float)
        avg_cpc = filtered_cpc.mean() if not filtered_cpc.empty and not filtered_cpc.isna().all() else 0
        logger.info(f"平均CPC (sum >= {settings.observe_highcpc_min_sum}): {avg_cpc:.2f}")

        # 1. Exclude_Irrelevant: avg_similarity <= 25
        df['Exclude_Irrelevant'] = (
            df['avg_similarity'].fillna(0) <= settings.exclude_irrelevant_similarity_threshold
        )

        # 2. Exclude_ZeroClick: clicks = 0 且 sum >= 50
        df['Exclude_ZeroClick'] = (
            (df['clicks'].fillna(0) == 0) &
            (df['sum'].fillna(0) >= settings.exclude_zeroclick_min_sum)
        )

        # 3. Exclude_NoData: 相似度数据为空且sum < 20
        similarity_data_missing = (
            df['avg_similarity'].isna() &
            df['similar_count'].isna() &
            df['competitor_count'].isna() &
            df['valid_scores'].isna()
        )
        df['Exclude_NoData'] = (
            similarity_data_missing &
            (df['sum'].fillna(0) < settings.exclude_nodata_min_sum)
        )

        # 4. Observe_LowCTR: views >= 500 且 CTR < 0.02
        df['Observe_LowCTR'] = (
            (df['views'].fillna(0) >= settings.observe_lowctr_min_views) &
            (df['ctr'].fillna(0) < settings.observe_lowctr_ctr_threshold)
        )

        # 5. Observe_HighCPC: sum >= 20 且 CPC > AvgCPC * 2.5
        df['Observe_HighCPC'] = (
            (df['sum'].fillna(0) >= settings.observe_highcpc_min_sum) &
            (df['cpc'].fillna(0) > (avg_cpc * settings.observe_highcpc_multiplier))
        )

        # 6. Observe_LowSimilarity: avg_similarity <= 50 且 similar_count <= 2
        df['Observe_LowSimilarity'] = (
            (df['avg_similarity'].fillna(0) <= settings.observe_lowsimilarity_similarity_threshold) &
            (df['similar_count'].fillna(0) <= settings.observe_lowsimilarity_count_threshold)
        )

        # 7. Optimize_LowPerf: clicks < 5 且 avg_similarity <= 40 且 similar_count <= 0
        df['Optimize_LowPerf'] = (
            (df['clicks'].fillna(0) < settings.optimize_lowperf_max_clicks) &
            (df['avg_similarity'].fillna(0) <= settings.optimize_lowperf_similarity_threshold) &
            (df['similar_count'].fillna(0) <= settings.optimize_lowperf_count_threshold)
        )

        # 8. Keep: 如果以上所有标记都是False，或者是出单关键词，则Keep为True
        flag_columns = ['Exclude_Irrelevant', 'Exclude_ZeroClick', 'Exclude_NoData', 'Observe_LowCTR',
                       'Observe_HighCPC', 'Observe_LowSimilarity', 'Optimize_LowPerf']

        # 基础Keep规则：所有标记都为False
        basic_keep = ~df[flag_columns].any(axis=1)

        # 出单关键词强制保留：Order_Generating为True的关键词一定要保留
        order_generating_keep = df['Order_Generating'].fillna(False)

        # 最终Keep规则：基础Keep或出单关键词
        df['Keep'] = basic_keep | order_generating_keep

        # 9. 是否排除标记: 如果以上标签其中有1项是True且不是出单关键词，则排除项True
        df['是否排除标记'] = df[flag_columns].any(axis=1) & ~order_generating_keep

        # 统计各标记的数量
        logger.info("=== 分析标记统计 ===")
        for col in flag_columns + ['Order_Generating', 'Keep', '是否排除标记']:
            true_count = df[col].sum()
            total_count = len(df)
            percentage = (true_count / total_count * 100) if total_count > 0 else 0
            logger.info(f"{col}: {true_count}/{total_count} ({percentage:.1f}%)")

        # 特别统计出单关键词的保护情况
        if 'Order_Generating' in df.columns:
            order_generating_total = df['Order_Generating'].sum()
            if order_generating_total > 0:
                order_generating_protected = (df['Order_Generating'] & df['Keep']).sum()
                logger.info(f"出单关键词保护: {order_generating_protected}/{order_generating_total} 个出单关键词被保留")

        return df




