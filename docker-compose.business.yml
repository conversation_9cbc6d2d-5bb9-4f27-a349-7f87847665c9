version: '3.9'

networks:
  microservices:
    name: microservices
    external: true

services:
  keyword-analysis-service:
    build: .
    container_name: keyword-analysis-service-server
    hostname: keyword-analysis-service
    environment:
      # Consul 配置
      - CONSUL_HOST=${CONSUL_HOST:-consul}
      - CONSUL_PORT=${CONSUL_PORT:-8500}
      
      # 服务配置
      - SERVICE_NAME=keyword-analysis-service
      - SERVICE_PORT=${SERVICE_PORT:-8002}
      - SERVICE_TAGS=${SERVICE_TAGS:-api,business,keyword-analysis}
      - ENVIRONMENT=${ENVIRONMENT:-production}
      - HOST=0.0.0.0
      
      # 数据库配置
      - PG_HOST=${PG_HOST:-postgres}
      - PG_PORT=${PG_PORT:-5432}
      - PG_USER=${PG_USER}
      - PG_PASSWORD=${PG_PASSWORD}
      - PG_DB=${PG_DB}
      
      # Wildberries API配置
      - WB_API_KEYS=${WB_API_KEYS}
      - WB_ADV_API_BASE_URL=${WB_ADV_API_BASE_URL:-https://advert-api.wildberries.ru}
      - WB_ANALYTICS_API_BASE_URL=${WB_ANALYTICS_API_BASE_URL:-https://seller-analytics-api.wildberries.ru}
      
      # 其他配置
      - REQUEST_TIMEOUT=${REQUEST_TIMEOUT:-30}
      - MAX_RETRIES=${MAX_RETRIES:-3}
      - RATE_LIMIT_REQUESTS_PER_SECOND=${RATE_LIMIT_REQUESTS_PER_SECOND:-1}
      
    env_file:
      - .env
    ports:
      - "8002:8002"
    networks:
      - microservices
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--output-document=-", "http://localhost:8002/api/v1/health"]
      interval: 15s
      timeout: 10s
      retries: 3
      start_period: 30s
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
    # 安全配置
    user: "1000:1000"
    read_only: false  # 需要写入日志文件
    security_opt:
      - no-new-privileges:true
    # 挂载卷（可选，用于持久化日志）
    volumes:
      - ./logs:/app/logs:rw
    command: ["python", "run_service.py"]

  # 可选：独立的PostgreSQL数据库（如果不使用共享数据库）
  # keyword-analysis-postgres:
  #   image: postgres:16-alpine
  #   container_name: keyword-analysis-postgres
  #   hostname: keyword-analysis-postgres
  #   environment:
  #     - POSTGRES_DB=${PG_DB:-keyword_analysis}
  #     - POSTGRES_USER=${PG_USER:-keyword_user}
  #     - POSTGRES_PASSWORD=${PG_PASSWORD}
  #   volumes:
  #     - keyword_analysis_postgres_data:/var/lib/postgresql/data
  #   networks:
  #     - microservices
  #   healthcheck:
  #     test: ["CMD-SHELL", "pg_isready -U ${PG_USER:-keyword_user} -d ${PG_DB:-keyword_analysis}"]
  #     interval: 10s
  #     timeout: 5s
  #     retries: 5
  #   restart: unless-stopped
  #   deploy:
  #     resources:
  #       limits:
  #         cpus: '0.5'
  #         memory: 512M
  #       reservations:
  #         cpus: '0.25'
  #         memory: 256M

  # 可选：独立的Redis缓存
  # keyword-analysis-redis:
  #   image: redis:7-alpine
  #   container_name: keyword-analysis-redis
  #   hostname: keyword-analysis-redis
  #   command: redis-server --appendonly yes
  #   volumes:
  #     - keyword_analysis_redis_data:/data
  #   networks:
  #     - microservices
  #   healthcheck:
  #     test: ["CMD", "redis-cli", "ping"]
  #     interval: 10s
  #     timeout: 3s
  #     retries: 3
  #   restart: unless-stopped
  #   deploy:
  #     resources:
  #       limits:
  #         cpus: '0.25'
  #         memory: 256M
  #       reservations:
  #         cpus: '0.1'
  #         memory: 128M

# 可选：数据卷定义
# volumes:
#   keyword_analysis_postgres_data:
#     driver: local
#   keyword_analysis_redis_data:
#     driver: local
