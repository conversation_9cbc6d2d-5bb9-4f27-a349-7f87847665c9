"""
API路由定义
"""
import os
import pandas as pd
from datetime import datetime
from typing import List, Dict, Any
from fastapi import APIRouter, HTTPException, Depends
from loguru import logger

from .models import CampaignAnalysisResponse, KeywordAnalysisResponse, HealthResponse, ServiceInfoResponse
from ..services.wb_api_client import MultiAccountAPIManager
from ..services.data_processor import KeywordAnalyzer
from ..models.campaign import Campaign

router = APIRouter()

# 全局API管理器实例
api_manager = None


def get_api_manager() -> MultiAccountAPIManager:
    """获取API管理器实例"""
    global api_manager
    if api_manager is None:
        api_manager = MultiAccountAPIManager()
    return api_manager


@router.get("/health", response_model=HealthResponse)
async def health_check():
    """健康检查端点"""
    return HealthResponse(
        status="healthy",
        service=os.getenv("SERVICE_NAME", "keyword-analysis-service"),
        timestamp=datetime.now().isoformat()
    )


@router.get("/info", response_model=ServiceInfoResponse)
async def service_info():
    """服务信息端点"""
    return ServiceInfoResponse(
        service=os.getenv("SERVICE_NAME", "keyword-analysis-service"),
        version="1.0.0",
        environment=os.getenv("ENVIRONMENT", "production"),
        tags=os.getenv("SERVICE_TAGS", "api,business,keyword-analysis").split(",")
    )


@router.get("/campaign/{campaign_id}/keywords", response_model=CampaignAnalysisResponse)
async def get_campaign_keywords_analysis(
    campaign_id: int,
    api_manager: MultiAccountAPIManager = Depends(get_api_manager)
):
    """
    通过广告活动ID查询关键词排除情况
    
    Args:
        campaign_id: 广告活动ID
        
    Returns:
        CampaignAnalysisResponse: 包含关键词分析结果的响应
    """
    try:
        logger.info(f"开始分析广告活动 {campaign_id} 的关键词")
        
        # 检查是否有可用的API客户端
        if not api_manager.clients:
            raise HTTPException(status_code=500, detail="没有可用的API客户端")
        
        # 使用第一个API客户端
        api_client = api_manager.clients[0]
        
        # 获取广告活动信息
        campaigns = api_client.get_campaigns()
        target_campaign = None
        
        for campaign in campaigns:
            if campaign.campaign_id == campaign_id:
                target_campaign = campaign
                break
        
        if not target_campaign:
            raise HTTPException(status_code=404, detail=f"未找到广告活动 {campaign_id}")
        
        # 检查是否为拍卖广告
        if target_campaign.type != 9 or target_campaign.status != 9:
            raise HTTPException(
                status_code=400, 
                detail=f"广告活动 {campaign_id} 不是有效的拍卖广告 (type={target_campaign.type}, status={target_campaign.status})"
            )
        
        # 分析关键词
        analyzer = KeywordAnalyzer(api_client)
        df_result = analyzer.analyze_campaign(target_campaign)
        
        if df_result.empty:
            return CampaignAnalysisResponse(
                campaign_id=campaign_id,
                total_keywords=0,
                keywords=[],
                summary={"message": "该广告活动没有关键词数据"}
            )
        
        # 转换DataFrame为响应模型
        keywords = []
        for _, row in df_result.iterrows():
            keyword_response = KeywordAnalysisResponse(
                campaign_id=int(row.get('campaign_id', campaign_id)),
                nm_id=int(row.get('nm_id', 0)),
                keyword=str(row.get('keyword', '')),
                avg_similarity=float(row['avg_similarity']) if pd.notna(row.get('avg_similarity')) else None,
                similar_count=int(row['similar_count']) if pd.notna(row.get('similar_count')) else None,
                competitor_count=int(row['competitor_count']) if pd.notna(row.get('competitor_count')) else None,
                valid_scores=int(row['valid_scores']) if pd.notna(row.get('valid_scores')) else None,
                views=int(row['views']) if pd.notna(row.get('views')) else None,
                sum=float(row['sum']) if pd.notna(row.get('sum')) else None,
                clicks=int(row['clicks']) if pd.notna(row.get('clicks')) else None,
                ctr=float(row['ctr']) if pd.notna(row.get('ctr')) else None,
                cpc=float(row['cpc']) if pd.notna(row.get('cpc')) else None,
                count=int(row['count']) if pd.notna(row.get('count')) else None,
                order_generating=bool(row.get('Order_Generating', False)),
                exclude_irrelevant=bool(row.get('Exclude_Irrelevant', False)),
                exclude_zero_click=bool(row.get('Exclude_ZeroClick', False)),
                exclude_no_data=bool(row.get('Exclude_NoData', False)),
                observe_low_ctr=bool(row.get('Observe_LowCTR', False)),
                observe_high_cpc=bool(row.get('Observe_HighCPC', False)),
                observe_low_similarity=bool(row.get('Observe_LowSimilarity', False)),
                optimize_low_perf=bool(row.get('Optimize_LowPerf', False)),
                keep=bool(row.get('Keep', True)),
                should_exclude=bool(row.get('是否排除标记', False))
            )
            keywords.append(keyword_response)
        
        # 生成统计摘要
        summary = _generate_summary(df_result)
        
        logger.info(f"广告活动 {campaign_id} 分析完成，共 {len(keywords)} 个关键词")
        
        return CampaignAnalysisResponse(
            campaign_id=campaign_id,
            total_keywords=len(keywords),
            keywords=keywords,
            summary=summary
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"分析广告活动 {campaign_id} 时发生错误: {e}")
        raise HTTPException(status_code=500, detail=f"分析失败: {str(e)}")


def _generate_summary(df: pd.DataFrame) -> Dict[str, Any]:
    """生成统计摘要"""
    total_keywords = len(df)
    
    if total_keywords == 0:
        return {"total_keywords": 0}
    
    summary = {
        "total_keywords": total_keywords,
        "order_generating_keywords": int(df.get('Order_Generating', pd.Series([False] * total_keywords)).sum()),
        "keywords_to_exclude": int(df.get('是否排除标记', pd.Series([False] * total_keywords)).sum()),
        "keywords_to_keep": int(df.get('Keep', pd.Series([True] * total_keywords)).sum()),
        "exclude_categories": {
            "irrelevant": int(df.get('Exclude_Irrelevant', pd.Series([False] * total_keywords)).sum()),
            "zero_click": int(df.get('Exclude_ZeroClick', pd.Series([False] * total_keywords)).sum()),
            "no_data": int(df.get('Exclude_NoData', pd.Series([False] * total_keywords)).sum())
        },
        "observe_categories": {
            "low_ctr": int(df.get('Observe_LowCTR', pd.Series([False] * total_keywords)).sum()),
            "high_cpc": int(df.get('Observe_HighCPC', pd.Series([False] * total_keywords)).sum()),
            "low_similarity": int(df.get('Observe_LowSimilarity', pd.Series([False] * total_keywords)).sum())
        },
        "optimize_categories": {
            "low_performance": int(df.get('Optimize_LowPerf', pd.Series([False] * total_keywords)).sum())
        }
    }
    
    # 计算百分比
    if total_keywords > 0:
        summary["percentages"] = {
            "order_generating": round(summary["order_generating_keywords"] / total_keywords * 100, 2),
            "to_exclude": round(summary["keywords_to_exclude"] / total_keywords * 100, 2),
            "to_keep": round(summary["keywords_to_keep"] / total_keywords * 100, 2)
        }
    
    return summary
