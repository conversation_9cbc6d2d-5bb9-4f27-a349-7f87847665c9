"""
Consul服务注册和发现
"""
import aiohttp
import asyncio
import os
import signal
from typing import Optional
from loguru import logger


class ConsulService:
    """Consul服务管理类"""
    
    def __init__(self):
        self.consul_host = os.getenv("CONSUL_HOST", "consul")
        self.consul_port = int(os.getenv("CONSUL_PORT", "8500"))
        self.service_name = os.getenv("SERVICE_NAME", "keyword-analysis-service")
        self.service_port = int(os.getenv("SERVICE_PORT", "8000"))
        self.service_tags = os.getenv("SERVICE_TAGS", "api,business,keyword-analysis").split(",")
        self.hostname = os.getenv("HOSTNAME", "localhost")
        self.service_id = f"{self.service_name}-{self.hostname}"
        
    async def register_service(self):
        """注册服务到Consul"""
        service_data = {
            "ID": self.service_id,
            "Name": self.service_name,
            "Tags": self.service_tags,
            "Address": self.hostname,
            "Port": self.service_port,
            "Check": {
                "HTTP": f"http://{self.hostname}:{self.service_port}/api/v1/health",
                "Interval": "10s",
                "Timeout": "5s",
                "DeregisterCriticalServiceAfter": "30s"
            },
            "Meta": {
                "version": "1.0.0",
                "environment": os.getenv("ENVIRONMENT", "production")
            }
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                url = f"http://{self.consul_host}:{self.consul_port}/v1/agent/service/register"
                async with session.put(url, json=service_data) as response:
                    if response.status == 200:
                        logger.info(f"✓ 服务 {self.service_name} (ID: {self.service_id}) 注册成功")
                        logger.info(f"  - 地址: {self.hostname}:{self.service_port}")
                        logger.info(f"  - 标签: {', '.join(self.service_tags)}")
                        logger.info(f"  - 健康检查: http://{self.hostname}:{self.service_port}/api/v1/health")
                    else:
                        error_text = await response.text()
                        logger.error(f"✗ 服务注册失败: HTTP {response.status} - {error_text}")
                        raise Exception(f"服务注册失败: {response.status}")
        except Exception as e:
            logger.error(f"✗ 连接Consul失败: {e}")
            raise
    
    async def deregister_service(self):
        """从Consul注销服务"""
        try:
            async with aiohttp.ClientSession() as session:
                url = f"http://{self.consul_host}:{self.consul_port}/v1/agent/service/deregister/{self.service_id}"
                async with session.put(url) as response:
                    if response.status == 200:
                        logger.info(f"✓ 服务 {self.service_id} 注销成功")
                    else:
                        logger.error(f"✗ 服务注销失败: {response.status}")
        except Exception as e:
            logger.error(f"✗ 注销服务时连接Consul失败: {e}")
    
    async def get_service_health(self) -> bool:
        """检查服务健康状态"""
        try:
            async with aiohttp.ClientSession() as session:
                url = f"http://{self.consul_host}:{self.consul_port}/v1/health/service/{self.service_name}"
                async with session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()
                        # 检查是否有健康的服务实例
                        for service in data:
                            checks = service.get('Checks', [])
                            if all(check.get('Status') == 'passing' for check in checks):
                                return True
                        return False
                    return False
        except Exception as e:
            logger.error(f"检查服务健康状态失败: {e}")
            return False
    
    async def discover_services(self, service_name: str) -> list:
        """发现其他服务"""
        try:
            async with aiohttp.ClientSession() as session:
                url = f"http://{self.consul_host}:{self.consul_port}/v1/health/service/{service_name}?passing=true"
                async with session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()
                        services = []
                        for service in data:
                            service_info = service.get('Service', {})
                            services.append({
                                'id': service_info.get('ID'),
                                'address': service_info.get('Address'),
                                'port': service_info.get('Port'),
                                'tags': service_info.get('Tags', [])
                            })
                        return services
                    return []
        except Exception as e:
            logger.error(f"发现服务 {service_name} 失败: {e}")
            return []


class GracefulShutdown:
    """优雅关闭处理器"""
    
    def __init__(self, consul_service: ConsulService):
        self.consul_service = consul_service
        self.should_exit = False
        
    def setup_signal_handlers(self):
        """设置信号处理器"""
        signal.signal(signal.SIGTERM, self.handle_signal)
        signal.signal(signal.SIGINT, self.handle_signal)
        
    def handle_signal(self, signum, frame):
        """处理关闭信号"""
        logger.info(f"收到信号 {signum}，开始优雅关闭...")
        self.should_exit = True
        
    async def shutdown(self):
        """执行优雅关闭"""
        logger.info("开始优雅关闭流程...")
        
        # 注销服务
        await self.consul_service.deregister_service()
        
        # 等待现有请求完成
        logger.info("等待现有请求完成...")
        await asyncio.sleep(5)
        
        logger.info("服务已优雅关闭")


# 全局实例
consul_service = ConsulService()
graceful_shutdown = GracefulShutdown(consul_service)
