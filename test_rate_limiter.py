"""
测试新的速率限制器
"""
import os
import sys
import time
from loguru import logger

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from rate_limiter import rate_limiter, APIRateLimiter
from wb_api_client import WildberriesAPIClient
from config import settings


def test_rate_limiter_basic():
    """测试速率限制器基本功能"""
    logger.info("=== 测试速率限制器基本功能 ===")
    
    # 创建新的限制器实例用于测试
    test_limiter = APIRateLimiter()
    
    # 测试不同API类型的限制
    api_types = ['adv_api', 'analytics_api', 'search_texts_api', 'default']
    
    for api_type in api_types:
        logger.info(f"测试 {api_type} 的速率限制...")
        
        # 记录开始时间
        start_time = time.time()
        
        # 连续发起3个请求
        for i in range(3):
            logger.info(f"  请求 {i+1}/3")
            test_limiter.wait_if_needed(api_type, f"test_endpoint_{i}")
        
        # 记录总耗时
        total_time = time.time() - start_time
        rate_limit = test_limiter.RATE_LIMITS[api_type]
        expected_min_time = rate_limit.min_interval * 2  # 2个间隔
        
        logger.info(f"  {api_type} 总耗时: {total_time:.2f}秒")
        logger.info(f"  预期最小耗时: {expected_min_time:.2f}秒")
        logger.info(f"  速率限制配置: {rate_limit}")
        
        if total_time >= expected_min_time * 0.9:  # 允许10%的误差
            logger.info(f"  ✅ {api_type} 速率限制正常")
        else:
            logger.warning(f"  ⚠️ {api_type} 速率限制可能有问题")
        
        print()


def test_url_classification():
    """测试URL分类功能"""
    logger.info("=== 测试URL分类功能 ===")
    
    test_urls = [
        ("https://advert-api.wildberries.ru/adv/v1/promotion/count", "adv_api"),
        ("https://seller-analytics-api.wildberries.ru/api/v2/nm-report/detail", "analytics_api"),
        ("https://seller-analytics-api.wildberries.ru/api/v2/search-report/product/search-texts", "search_texts_api"),
        ("https://other-api.example.com/test", "default")
    ]
    
    for url, expected_type in test_urls:
        actual_type = rate_limiter.get_api_type_for_url(url)
        status = "✅" if actual_type == expected_type else "❌"
        logger.info(f"{status} {url} -> {actual_type} (期望: {expected_type})")


def test_rate_limiter_stats():
    """测试速率限制器统计功能"""
    logger.info("=== 测试速率限制器统计功能 ===")
    
    # 发起一些测试请求
    rate_limiter.wait_if_needed('adv_api', 'test1')
    rate_limiter.wait_if_needed('analytics_api', 'test2')
    rate_limiter.wait_if_needed('search_texts_api', 'test3')
    
    # 获取统计信息
    stats = rate_limiter.get_stats()
    
    logger.info("速率限制器统计信息:")
    for api_type, stat in stats.items():
        logger.info(f"  {api_type}:")
        logger.info(f"    总请求数: {stat['total_requests']}")
        logger.info(f"    本分钟请求数: {stat['requests_this_minute']}")
        if stat['last_request_ago'] is not None:
            logger.info(f"    上次请求: {stat['last_request_ago']:.2f}秒前")
        logger.info(f"    速率限制: {stat['rate_limit']}")


def test_with_real_api():
    """使用真实API测试速率限制器"""
    logger.info("=== 使用真实API测试速率限制器 ===")
    
    try:
        # 检查API密钥配置
        if not hasattr(settings, 'api_keys_list') or not settings.api_keys_list:
            logger.warning("未配置API密钥，跳过真实API测试")
            return
        
        # 创建API客户端
        api_key = settings.api_keys_list[0]
        client = WildberriesAPIClient(api_key)
        
        logger.info("测试真实API调用的速率限制...")
        
        # 记录开始时间
        start_time = time.time()
        
        # 连续调用几个不同的API
        logger.info("1. 获取广告活动列表...")
        campaigns = client.get_campaigns()
        logger.info(f"   获取到 {len(campaigns)} 个广告活动")
        
        if campaigns:
            campaign = campaigns[0]
            
            logger.info("2. 获取关键词...")
            keywords = client.get_campaign_keywords(campaign.campaign_id)
            logger.info(f"   获取到 {len(keywords)} 个关键词")
            
            logger.info("3. 获取产品列表...")
            products = client.get_campaign_products(campaign.campaign_id)
            logger.info(f"   获取到 {len(products)} 个产品")
        
        # 记录总耗时
        total_time = time.time() - start_time
        logger.info(f"真实API测试总耗时: {total_time:.2f}秒")
        
        # 显示速率限制器统计
        stats = rate_limiter.get_stats()
        logger.info("API调用统计:")
        for api_type, stat in stats.items():
            if stat['total_requests'] > 0:
                logger.info(f"  {api_type}: {stat['total_requests']} 次请求")
        
    except Exception as e:
        logger.error(f"真实API测试失败: {e}")


def test_minute_limit():
    """测试每分钟请求限制"""
    logger.info("=== 测试每分钟请求限制 ===")
    logger.info("注意：此测试需要较长时间，仅演示逻辑")
    
    # 创建测试限制器，使用更小的限制便于测试
    test_limiter = APIRateLimiter()
    
    # 修改search_texts_api的限制为更小的值便于测试
    original_limit = test_limiter.RATE_LIMITS['search_texts_api']
    test_limiter.RATE_LIMITS['search_texts_api'].requests_per_minute = 2  # 改为每分钟2个请求
    
    logger.info("模拟search_texts_api每分钟2个请求的限制...")
    
    start_time = time.time()
    
    # 尝试发起3个请求（应该触发分钟限制）
    for i in range(3):
        logger.info(f"发起第 {i+1} 个请求...")
        test_limiter.wait_if_needed('search_texts_api', f'test_request_{i}')
        elapsed = time.time() - start_time
        logger.info(f"  累计耗时: {elapsed:.1f}秒")
    
    total_time = time.time() - start_time
    logger.info(f"3个请求总耗时: {total_time:.1f}秒")
    
    # 恢复原始限制
    test_limiter.RATE_LIMITS['search_texts_api'] = original_limit


def main():
    """主测试函数"""
    logger.info("开始测试新的速率限制器...")
    
    # 1. 基本功能测试
    test_rate_limiter_basic()
    
    print("\n" + "="*60 + "\n")
    
    # 2. URL分类测试
    test_url_classification()
    
    print("\n" + "="*60 + "\n")
    
    # 3. 统计功能测试
    test_rate_limiter_stats()
    
    print("\n" + "="*60 + "\n")
    
    # 4. 真实API测试
    test_with_real_api()
    
    print("\n" + "="*60 + "\n")
    
    # 5. 分钟限制测试（可选，耗时较长）
    # test_minute_limit()
    
    logger.info("速率限制器测试完成")


if __name__ == "__main__":
    main()
