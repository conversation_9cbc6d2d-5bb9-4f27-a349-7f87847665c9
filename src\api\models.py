"""
API响应模型
"""
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field


class KeywordAnalysisResponse(BaseModel):
    """关键词分析响应模型"""
    campaign_id: int = Field(..., description="广告活动ID")
    nm_id: int = Field(..., description="商品ID")
    keyword: str = Field(..., description="关键词")
    avg_similarity: Optional[float] = Field(None, description="平均相似度")
    similar_count: Optional[int] = Field(None, description="相似商品数量")
    competitor_count: Optional[int] = Field(None, description="竞争对手数量")
    valid_scores: Optional[int] = Field(None, description="有效评分数")
    views: Optional[int] = Field(None, description="展示次数")
    sum: Optional[float] = Field(None, description="花费金额")
    clicks: Optional[int] = Field(None, description="点击次数")
    ctr: Optional[float] = Field(None, description="点击率")
    cpc: Optional[float] = Field(None, description="平均点击成本")
    count: Optional[int] = Field(None, description="关键词热度")
    order_generating: bool = Field(False, description="是否为出单关键词")
    exclude_irrelevant: bool = Field(False, description="排除不相关")
    exclude_zero_click: bool = Field(False, description="排除零点击")
    exclude_no_data: bool = Field(False, description="排除无数据")
    observe_low_ctr: bool = Field(False, description="观察低CTR")
    observe_high_cpc: bool = Field(False, description="观察高CPC")
    observe_low_similarity: bool = Field(False, description="观察低相似度")
    optimize_low_perf: bool = Field(False, description="优化低表现")
    keep: bool = Field(True, description="保持不变")
    should_exclude: bool = Field(False, description="是否应该排除")


class CampaignAnalysisResponse(BaseModel):
    """广告活动分析响应模型"""
    campaign_id: int = Field(..., description="广告活动ID")
    total_keywords: int = Field(..., description="关键词总数")
    keywords: List[KeywordAnalysisResponse] = Field(..., description="关键词分析结果")
    summary: Dict[str, Any] = Field(..., description="统计摘要")


class HealthResponse(BaseModel):
    """健康检查响应模型"""
    status: str = Field("healthy", description="服务状态")
    service: str = Field(..., description="服务名称")
    timestamp: str = Field(..., description="检查时间")


class ServiceInfoResponse(BaseModel):
    """服务信息响应模型"""
    service: str = Field(..., description="服务名称")
    version: str = Field(..., description="服务版本")
    environment: str = Field(..., description="运行环境")
    tags: List[str] = Field(..., description="服务标签")
