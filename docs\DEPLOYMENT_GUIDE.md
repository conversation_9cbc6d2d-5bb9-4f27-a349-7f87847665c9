# 关键词分析微服务部署指南

本文档详细说明如何部署关键词分析微服务到基于 Consul 的微服务架构中。

## 📋 目录

- [服务概述](#服务概述)
- [前置条件](#前置条件)
- [快速部署](#快速部署)
- [配置说明](#配置说明)
- [API接口](#api接口)
- [监控和维护](#监控和维护)
- [故障排除](#故障排除)

## 🏗️ 服务概述

### 服务功能

关键词分析微服务提供以下功能：

- **关键词分析**: 通过广告ID查询关键词排除情况
- **数据处理**: 分析关键词效益并生成排除建议
- **服务发现**: 自动注册到Consul并支持健康检查
- **API接口**: 提供RESTful API接口

### 技术栈

- **框架**: FastAPI + Uvicorn
- **数据库**: PostgreSQL
- **服务发现**: Consul
- **容器化**: Docker + Docker Compose

## ✅ 前置条件

### 1. 基础设施服务已部署

确保以下基础设施服务正在运行：

```bash
# 检查基础设施服务状态
docker-compose -f docker-compose.infrastructure.yml ps

# 应该看到以下服务正在运行：
# - consul-server
# - consul-template  
# - nginx-gateway
# - redis-cache (可选)
# - postgres-db
# - consul-ui-proxy
```

### 2. 网络连通性测试

```bash
# 测试 Consul 连接
curl http://localhost:8500/v1/status/leader

# 测试网关连接
curl http://localhost/gateway/status
```

### 3. 数据库准备

确保PostgreSQL数据库中存在必要的表结构和数据。

## 🚀 快速部署

### 步骤 1: 准备配置文件

```bash
# 复制环境变量模板
cp .env.business.example .env

# 编辑配置文件
nano .env
```

### 步骤 2: 修改环境变量

编辑 `.env` 文件，配置以下关键参数：

```bash
# 数据库连接
PG_HOST=postgres  # 或实际数据库主机IP
PG_USER=your_actual_db_user
PG_PASSWORD=your_actual_db_password
PG_DB=your_actual_database

# Wildberries API密钥
WB_API_KEYS=your_actual_api_key_1,your_actual_api_key_2

# 如果基础设施在其他主机
# CONSUL_HOST=*************
```

### 步骤 3: 启动服务

```bash
# 构建并启动服务
docker-compose -f docker-compose.business.yml up -d

# 检查服务状态
docker-compose -f docker-compose.business.yml ps
```

### 步骤 4: 验证部署

```bash
# 检查服务是否注册到 Consul
curl http://localhost:8500/v1/catalog/service/keyword-analysis-service

# 检查服务健康状态
curl http://localhost:8500/v1/health/service/keyword-analysis-service

# 通过网关访问服务
curl http://localhost/api/keyword-analysis-service/api/v1/health

# 直接访问服务（用于调试）
curl http://localhost:8000/api/v1/health
```

## ⚙️ 配置说明

### 环境变量详解

| 变量名 | 描述 | 默认值 | 必需 |
|--------|------|--------|------|
| `SERVICE_NAME` | 服务名称 | `keyword-analysis-service` | 否 |
| `SERVICE_PORT` | 服务端口 | `8000` | 否 |
| `CONSUL_HOST` | Consul主机地址 | `consul` | 否 |
| `PG_HOST` | PostgreSQL主机 | `postgres` | 否 |
| `PG_USER` | 数据库用户名 | - | 是 |
| `PG_PASSWORD` | 数据库密码 | - | 是 |
| `PG_DB` | 数据库名称 | - | 是 |
| `WB_API_KEYS` | Wildberries API密钥 | - | 是 |

### 服务标签

服务会自动注册以下标签到Consul：
- `api`: 表示这是一个API服务
- `business`: 表示这是业务服务
- `keyword-analysis`: 服务特定标签

## 📡 API接口

### 健康检查

```bash
GET /api/v1/health
```

响应示例：
```json
{
  "status": "healthy",
  "service": "keyword-analysis-service",
  "timestamp": "2024-01-01T12:00:00"
}
```

### 服务信息

```bash
GET /api/v1/info
```

### 关键词分析

```bash
GET /api/v1/campaign/{campaign_id}/keywords
```

响应示例：
```json
{
  "campaign_id": 26771183,
  "total_keywords": 1009,
  "keywords": [
    {
      "campaign_id": 26771183,
      "nm_id": 253486273,
      "keyword": "люстра на потолок",
      "avg_similarity": 60.0,
      "similar_count": 5,
      "views": 106729,
      "clicks": 3985,
      "ctr": 0.04,
      "should_exclude": false,
      "keep": true
    }
  ],
  "summary": {
    "total_keywords": 1009,
    "keywords_to_exclude": 234,
    "keywords_to_keep": 775,
    "percentages": {
      "to_exclude": 23.2,
      "to_keep": 76.8
    }
  }
}
```

## 📊 监控和维护

### 日志查看

```bash
# 查看服务日志
docker-compose -f docker-compose.business.yml logs -f keyword-analysis-service

# 查看最近100行日志
docker-compose -f docker-compose.business.yml logs --tail=100 keyword-analysis-service
```

### 服务扩展

```bash
# 扩展到3个实例
docker-compose -f docker-compose.business.yml up -d --scale keyword-analysis-service=3

# 验证负载均衡
for i in {1..10}; do
  curl http://localhost/api/keyword-analysis-service/api/v1/info
done
```

### 服务重启

```bash
# 重启服务
docker-compose -f docker-compose.business.yml restart keyword-analysis-service

# 重新构建并重启
docker-compose -f docker-compose.business.yml up -d --build keyword-analysis-service
```

## 🔧 故障排除

### 常见问题

#### 1. 服务无法启动

**症状**: 容器启动失败或立即退出

**解决方案**:
```bash
# 查看详细日志
docker-compose -f docker-compose.business.yml logs keyword-analysis-service

# 检查配置文件
docker-compose -f docker-compose.business.yml config

# 检查环境变量
docker exec keyword-analysis-service-server env | grep -E "(PG_|WB_|CONSUL_)"
```

#### 2. 无法连接数据库

**症状**: 服务启动但API返回数据库连接错误

**解决方案**:
```bash
# 测试数据库连接
docker exec keyword-analysis-service-server python -c "
from src.core.database import db_manager
print('Testing database connection...')
try:
    db_manager.test_connection()
    print('Database connection successful!')
except Exception as e:
    print(f'Database connection failed: {e}')
"
```

#### 3. Consul注册失败

**症状**: 服务启动但在Consul UI中看不到

**解决方案**:
```bash
# 检查网络连接
docker exec keyword-analysis-service-server ping consul

# 检查Consul配置
docker exec keyword-analysis-service-server env | grep CONSUL

# 手动测试Consul连接
docker exec keyword-analysis-service-server curl http://consul:8500/v1/status/leader
```

#### 4. API请求失败

**症状**: 通过网关访问返回502或404

**解决方案**:
```bash
# 检查Nginx配置
docker exec nginx-gateway cat /etc/nginx/conf.d/services.conf | grep keyword-analysis

# 重新加载Nginx配置
docker exec nginx-gateway nginx -s reload

# 检查consul-template日志
docker-compose -f docker-compose.infrastructure.yml logs consul-template
```

### 性能调优

#### 资源限制调整

编辑 `docker-compose.business.yml`:

```yaml
deploy:
  resources:
    limits:
      cpus: '2.0'      # 增加CPU限制
      memory: 2G       # 增加内存限制
    reservations:
      cpus: '1.0'      # 增加CPU预留
      memory: 1G       # 增加内存预留
```

#### 并发处理优化

在环境变量中添加：

```bash
# Uvicorn工作进程数
WORKERS=4

# 数据库连接池大小
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
```

## 📞 支持和帮助

### 常用命令速查

```bash
# 查看服务状态
docker-compose -f docker-compose.business.yml ps

# 查看服务日志
docker-compose -f docker-compose.business.yml logs -f keyword-analysis-service

# 重启服务
docker-compose -f docker-compose.business.yml restart keyword-analysis-service

# 查看Consul服务
curl http://localhost:8500/v1/catalog/services

# 通过网关访问
curl http://localhost/api/keyword-analysis-service/api/v1/health
```

### 问题诊断清单

- [ ] 基础设施服务是否正常运行？
- [ ] 数据库连接配置是否正确？
- [ ] Wildberries API密钥是否有效？
- [ ] 网络连接是否正常？
- [ ] 服务是否成功注册到Consul？
- [ ] 健康检查是否通过？
- [ ] Nginx配置是否正确生成？

---

**文档版本**: v1.0  
**最后更新**: 2024-01-XX  
**维护者**: 开发团队
